import React, { useEffect, useRef } from 'react';
import { useChat } from '../hooks/useChat';
import MessageBubble from './MessageBubble';
// import BotStatusIndicator from './BotStatusIndicator';
import SkeletonLoader from './SkeletonLoader';
import ThinkingIndicator from './ThinkingIndicator';
import Logo from '../assets/img/logo.png';
import { usePage } from '@/hooks/usePage';
import { ScrollArea } from './ui/scroll-area';

const ChatArea: React.FC<{ isWidget?: boolean }> = ({ isWidget }) => {
  const { botState, agent, currentSession, messages, isLoading } = useChat();
  const { hasMore, page, setPage } = usePage({ currentSession });
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatBodyRef = useRef<HTMLDivElement>(null);
  const thinkingStepsList = [
    'Analyzing input...',
    'Searching knowledge base...',
    'Formulating response...',
    'Refining answer...'
  ];
  const suggestions = [
    { icon: "🖼️", text: "Get Company Details", color: "text-green-600" },
    { icon: "💬", text: "Get Product & Service Support", color: "text-orange-600" },
    { icon: "📅", text: "Schedule Sales Demo", color: "text-blue-600" },
    { icon: "📄", text: "Get Sales Report", color: "text-blue-600" }
  ];

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current && page === 1) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [currentSession?.messages, botState, page]);

  // Add scroll event listener for infinite scrolling
  useEffect(() => {
    const handleScroll = () => {
      const chatBody = chatBodyRef.current;
      if (chatBody && chatBody.scrollTop === 0 && hasMore && botState.status === 'idle') {
        setPage(prevPage => prevPage + 1);
      }
    };

    const chatBody = chatBodyRef.current;
    if (chatBody) {
      chatBody.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (chatBody) {
        chatBody.removeEventListener('scroll', handleScroll);
      }
    };
  }, [hasMore, botState.status, setPage]);

  // Show welcome screen when no messages
  if (!currentSession || messages.length === 0) {
    return (
      <>
        <div className={`flex-1 flex flex-col items-center ${isWidget ? 'chat-widget' : ''} justify-center bg-white dark:bg-gray-800 px-4`}>
          <div className="text-center max-w-2xl">
            <div className="text-6xl mb-5">
              <img src={Logo} className='mx-auto' alt="logo" style={{ width: '120px', height: 'auto' }} />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              How can I help with?
            </h1>
            <p className="text-md text-gray-600 dark:text-gray-400 mb-8">
              I'm GoBot {agent.replace('_', ' ')}, your AI-powered assistant ready to help you with your needs.
            </p>
            <div className='flex justify-center mb-1'>
              {
                suggestions.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8 max-w-md">
                    {suggestions.slice(0, 4).map((suggestion, index) => (
                      <button
                        key={index}
                        className="flex items-center space-x-3 p-4 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left"
                      >
                        <span className={suggestion.color}>{suggestion.icon}</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {suggestion.text}
                        </span>
                      </button>
                    ))}
                  </div>
                ) : ""
              }

            </div>
            {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">💬 Ask Questions</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Get instant answers with intelligent responses
                </p>
              </div>
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">🎤 Voice Input</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Use voice commands to interact naturally
                </p>
              </div>
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">📁 File Upload</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Share images and documents for analysis
                </p>
              </div>
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">🧠 Smart Responses</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Get contextual, helpful AI-powered responses
                </p>
              </div>
            </div> */}
          </div>
        </div>
      </>
    );
  }

  return (
    <div className={`flex-1 flex flex-col bg-white dark:bg-gray-800 min-h-0`}>
      {/* Messages Area */}
      <ScrollArea ref={chatBodyRef} className="flex-1 p-4 min-h-0">
        {isLoading ? (
          <SkeletonLoader type="message" count={3} />
        ) : (
          <>
            {/* Messages List */}
            {messages.map((message, index) => (
              <MessageBubble
                key={message.message_id || index}
                message={message}
                isWidget={isWidget}
                isStreaming={message.message_id === messages[messages.length - 1]?.message_id && botState.status === 'writing'}
              />
            ))}

            {/* Typing Indicator */}
            {botState.status !== 'idle' && (
              <div className="px-6 py-2">
                <ThinkingIndicator steps={thinkingStepsList} />
              </div>
            )}

            <div ref={messagesEndRef} />
          </>
        )}
      </ScrollArea>
    </div>
  );
};

export default ChatArea;
