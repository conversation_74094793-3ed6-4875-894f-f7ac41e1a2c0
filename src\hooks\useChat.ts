/* eslint-disable @typescript-eslint/no-explicit-any */
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { chatService } from '../services/Chat.service';
import { addMessage, updateMessage, setBotState } from '../store/slices/chatSlice';
import { setCurrentSession } from '../store/slices/authSlice';
import { setLoading } from '../store/slices/commonSlice';
import { ChatSession, Message } from '../types/chat';
import { v4 as uuidv4 } from 'uuid';
import { useRef } from 'react';

export const useChat = () => {
  const dispatch = useDispatch();
  const { sessions, currentSession } = useSelector((state: RootState) => state.auth);
  const { botState, messages } = useSelector((state: RootState) => state.chat);
  const { isLoading, token, userId, project, botName, type, ipAddress } = useSelector((state: RootState) => state.common);
  const controllerRef = useRef<AbortController>();

  const startNewChat = async (token: string, botName: string = 'Gobot', type: string = 'service', userId?: string, project?: string) => {
    try {
      return await chatService.initSession(token, userId, project, botName, type, ipAddress);
    } catch (err) {
      console.error('Error initializing session:', err);
      return {};
    }
  };

  const getSessionToken = async (session: ChatSession) => {
    try {
      await chatService.getSessionToken(session);
    } catch (err) {
      console.error('Error getting session token:', err);
    }
  };

  const deleteSession = async (sessionId: string) => {
    try {
      await chatService.deleteSession(sessionId);
    } catch (err) {
      console.error('Error deleting session:', err);
    }
  };

  const sendMessage = async (content: string, attachments?: any[]) => {
    let sessionId = currentSession?.id || '';
    if (!currentSession) {
      const session = await startNewChat(token, botName, type, userId, project);
      if (session?.id) {
        sessionId = session.id.toString();
      }
    }

    const userMessage: Message = {
      message_id: uuidv4(),
      content,
      type: 'human',
      timestamp: new Date().toISOString(),
      attachments
    };

    dispatch(addMessage(userMessage));
    dispatch(setBotState({
      status: 'thinking',
      thinkingSteps: [
        'Analyzing your message...',
        'Understanding context...',
        'Preparing response strategy...'
      ]
    }));

    try {
      const controller = new AbortController();
      controllerRef.current = controller;

      await chatService.sendMessage(
        sessionId,
        {
          message: content,
          project: project || '',
          user_id: userId || '',
        },
        type,
        controllerRef.current.signal,
        (message: string) => {
          if (message === 'END') {
            dispatch(setBotState({ status: 'idle' }));
          } else {
            dispatch(setBotState({
              status: 'writing',
              currentMessage: message
            }));
          }
        },
        (error: unknown) => {
          console.error('Error sending message:', error);
          dispatch(setBotState({ status: 'idle' }));
        }
      );
    } catch (err) {
      console.error('Error sending message:', err);
      dispatch(setBotState({ status: 'idle' }));
    }
  };

  const stopGeneration = () => {
    if (controllerRef.current) {
      controllerRef.current.abort();
      dispatch(setBotState({ status: 'idle' }));
    }
  };

  return {
    messages,
    sessions,
    currentSession,
    botState,
    isLoading,
    botName,
    userId,
    token,
    agent: type,
    ipAddress,
    getSessionToken,
    startNewChat,
    sendMessage,
    stopGeneration,
    setLoading,
    updateMessage,
    setCurrentSession,
    deleteSession
  };
};
