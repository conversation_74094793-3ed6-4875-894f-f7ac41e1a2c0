
import React, { useState, useEffect } from 'react';
import ChatSidebar from '../components/ChatSidebar';
import ChatArea from '../components/ChatArea';
import ChatInput from '../components/ChatInput';
import UserProfileDropdown from '../components/UserProfileDropdown';
import { useChat } from '../hooks/useChat';
import Logo from '../assets/img/logo.png';
import { AlignRight } from 'lucide-react';
import { useSearchParams } from 'react-router-dom';
import config from '@/config';
import { chatService } from '@/services/Chat.service';
import { useDispatch } from 'react-redux';
import { setDetails } from '@/store/slices/commonSlice';

const Chat: React.FC = () => {
  const dispatch = useDispatch();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { messages, currentSession, userId, ipAddress } = useChat();
  const hasMessages = currentSession && messages.length > 0;
  const [serachParams] = useSearchParams();
  const agent = serachParams.get('agent') || 'service';
  const token = serachParams.get('token') || '';

  // const startNewChat = async (token: string, botName: string = 'Gobot', type: string = 'service', userId?: string, project?: string) => {
  //   try {
  //     await chatService.initSession(token, userId, project, botName, type, ipAddress);
  //   } catch (err) {
  //     console.error('Error initializing session:', err);
  //   }
  // };

  useEffect(() => {
    const getSessions = async () => {
      try {
        await chatService.getSessions(userId, ipAddress);
      } catch (err) {
        console.error('Error getting sessions:', err);
      }
    }
    getSessions();
  }, [userId, ipAddress, agent, token]);

  useEffect(() => {
    const setAgentDetails = (token: string, agent: string) => {
      dispatch(setDetails({ token, type: agent }));
    };
    setAgentDetails(token, agent);
  }, [dispatch, agent, token]);



  return (
    <main className="h-screen w-screen flex bg-gray-100 dark:bg-gray-900 overflow-hidden fixed inset-0">
      {/* Sidebar */}
      <ChatSidebar
        isCollapsed={sidebarCollapsed}
        chatConfig={{ token, botName: config.botName, agent }}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      <div className="flex-1 flex flex-col min-h-0">
        {/* Header */}
        <div className="flex flex-row p-2 flex-shrink-0">
          <div className="flex-1">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                <AlignRight className="w-5 h-5" />
              </button>
              <img src={Logo} alt="logo" style={{ width: '120px', height: 'auto' }} />
            </div>
          </div>
          <div className="ml-auto">
            {/* <button onClick={handleNewChat} className="cursor-pointer">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            New Chat
          </button>

          <button onClick={handleDeleteAllChats} className="cursor-pointer text-red-600">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete Chats
          </button> */}
            <UserProfileDropdown />
          </div>
        </div>

        {/* Main Content */}
        <div className={`flex-1 flex flex-col min-h-0 ${hasMessages ? '' : 'justify-center'}`}>
          <div className={hasMessages ? 'flex-1 flex flex-col min-h-0' : 'flex-1 flex flex-col justify-center min-h-0'}>
            <ChatArea />
          </div>

          {/* Input positioned based on whether there are messages */}
          <div className={`flex-shrink-0 ${hasMessages ? '' : 'flex justify-center pb-8'}`}>
            <div className={hasMessages ? 'w-full' : 'w-full max-w-4xl px-4'}>
              <ChatInput />
            </div>
          </div>
        </div>

      </div>
    </main>
  );
};


export default Chat;
