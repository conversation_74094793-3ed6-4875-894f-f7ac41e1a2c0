import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { /* Paperclip,*/ Send, StopCircle, Ticket, UserPlus2, KeyIcon, X, PlusCircle } from 'lucide-react';
import ReactMarkdown from 'react-markdown'
import { addMessage, extendMessages, setMessages } from '../store/slices/chatSlice';
import { RootState } from '../store/store';
import { Attachment, Message } from '../types/chat';
// import { useNavigate } from 'react-router-dom';
// import { routes } from '../config/routes';
import 'bootstrap/dist/css/bootstrap.min.css';
import '../assets/css/chat.css';
import { chatService } from '../services/Chat.service';
import MessageContent from '../components/MessageContent';
import { useSearchParams } from 'react-router-dom';
import Logo from '../assets/img/logo.png'; // Assuming you have a logo image

export const UserChat: React.FC<{ token: string }> = ({ token }) => {
    const agentName = "GoBot";
    const [newMessage, setNewMessage] = useState('');
    const [error, setError] = useState('');
    const [nextStep, setNextSteps] = useState<string>();
    const [currentMessage, setCurrentMessage] = useState<string>('');
    const [isRendering, setRendering] = useState(false);
    const [isTyping, setIsTyping] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [attachments, setAttachments] = useState<Attachment[]>([]);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const controllerRef = useRef<AbortController>(undefined);
    const chatBodyRef = useRef<HTMLDivElement>(null);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    // const navigate = useNavigate();
    const dispatch = useDispatch();
    const { messages, suggestedTools } = useSelector((state: RootState) => state.chat);
    const { sessionId } = useSelector((state: RootState) => state.auth);
    const [serachParams] = useSearchParams();
    const agent = serachParams.get('agent') || 'service';

    const defaultSuggestedTools = [
        { icon: <Ticket size={20} />, text: 'Create support ticket', action: 'Create a new support ticket.' },
        { icon: <UserPlus2 size={20} />, text: 'Create account', action: 'Create a new user account for me.' },
        { icon: <Ticket size={20} />, text: 'Track ticket status', action: 'Retrieving ticket status.' },
        { icon: <KeyIcon size={20} />, text: 'Reset password', action: 'I need help to Reset password of my account' },
        { icon: <Ticket size={20} />, text: 'Escalate a ticket', action: 'Escalate a ticket to a human suport executive.' },
        { icon: <Ticket size={20} />, text: 'Resolve a ticket', action: 'Mark a ticket as resolved with a resolution note.' },
        { icon: <Ticket size={20} />, text: 'Reopen a ticket', action: 'Reopen a existing support ticket.' },
    ];
    const currentSuggestedTools = suggestedTools?.length ? suggestedTools : defaultSuggestedTools;

    // useEffect(() => {
    //     if (!sessionId) {
    //         navigate(routes.not_found);
    //     }
    // }, [sessionId, navigate]);
    useEffect(() => {
        const initSession = async (token: string = '') => {
            try {
                // await chatService.initSession(token, '', '', agentName, 'sales');
                // await chatService.initSession(token, '', '', agentName, 'service');
                await chatService.initSession(token, '', '', agentName, agent);
            } catch (err) {
                console.error('Error initializing session:', err);
            }
        };
        if (!sessionId && token) {
            initSession(token);
        }
    }, [token, sessionId, agentName, agent]);

    // Scroll to bottom when messages change
    useEffect(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    }, [messages, isTyping]);

    useEffect(() => {
        if (sessionId) {
            chatService.getMessages(sessionId, 10, page)
                .then(response => {
                    if (!response || response.length === 0) {
                        setHasMore(false);
                    } else {
                        if (page > 1) {
                            dispatch(extendMessages(response))
                        } else {
                            dispatch(setMessages(response))
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching messages:', error);
                    setHasMore(false);
                });
        }
    }, [sessionId, page, dispatch]);

    // Add scroll event listener
    useEffect(() => {
        const handleScroll = () => {
            const chatBody = chatBodyRef.current;
            if (chatBody && chatBody.scrollTop === 0 && hasMore && !isTyping) {
                setPage(prevPage => prevPage + 1);
            }
        };
        const chatBody = chatBodyRef.current;
        if (chatBody) {
            chatBody.addEventListener('scroll', handleScroll);
        }
        return () => {
            if (chatBody) {
                chatBody.removeEventListener('scroll', handleScroll);
            }
        };
    }, [hasMore, isTyping]);

    const sendMessageToApi = async (content: string) => {
        setIsTyping(true);
        try {
            const controller = new AbortController();
            controllerRef.current = controller;

            await chatService.sendMessage(
                sessionId || '',
                {
                    message: content,
                    project: '',
                    user_id: '',
                },
                controllerRef.current.signal,
                (messages: string) => {
                    if (messages == 'END') {
                        setRendering(false);
                        setIsTyping(false);
                    } else {
                        setCurrentMessage(messages);
                    }

                },
                (error: unknown) => {
                    console.error('Error sending message:', error);
                    if (error instanceof Error) {
                        setError(error.message);
                    }
                    setRendering(false);
                    setIsTyping(false);
                }
            );

        } catch (err: unknown) {
            console.error('Error sending message:', err);
            if (err instanceof Error) {
                setError(err.message);
            }
        }
    };

    // Handle send message
    const handleSendMessage = async () => {
        if (newMessage.trim() === '' && attachments.length === 0) return;
        const newMessageObj: Message = {
            id: Date.now().toString(),
            content: newMessage,
            type: 'human',
            attachments: [...attachments],
            timestamp: Date.now().toString(),
        };

        dispatch(addMessage(newMessageObj));
        setNewMessage('');
        setNextSteps('');
        setError('');
        setAttachments([]);

        // await sendMessageToApi(newMessage, attachments);
        await sendMessageToApi(newMessage);
    };

    const handleSendToolMessage = async (tool: string) => {
        if (tool.trim() === '') return;
        const newMessageObj: Message = {
            id: Date.now().toString(),
            content: tool,
            type: 'human',
            attachments: [],
            timestamp: Date.now().toString(),
        };

        dispatch(addMessage(newMessageObj));
        setNewMessage('');
        setNextSteps('');
        setError('');
        setAttachments([]);
        // await sendMessageToApi(tool, attachments);
        await sendMessageToApi(tool);
    };

    const newSession = async () => {
        try {
            // await chatService.initSession(token, '', '', agentName, 'sales');
            // await chatService.initSession(token, '', '', agentName, 'service');
            await chatService.initSession(token, '', '', agentName, agent);
        } catch (err) {
            console.error('Error initializing session:', err);
        }
    };
    // Handle file selection
    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e?.target?.files || []);

        files.forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                setAttachments(prev => [...prev, {
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    data: e?.target?.result || null
                }]);
            };
            reader.readAsDataURL(file);
        });
    };

    // Remove attachment
    const removeAttachment = (index: number) => {
        setAttachments(prev => prev.filter((_, i) => i !== index));
    };

    // Render attachment previews
    const renderAttachmentPreviews = () => {
        return attachments.map((file, index) => (
            <div key={index} className="attachment-preview">
                <span className="attachment-name">{file.name}</span>
                <button
                    className="btn btn-sm content-danger"
                    onClick={() => removeAttachment(index)}
                >
                    <X size={14} />
                </button>
            </div>
        ));
    };

    // Render messages
    const renderMessages = () => {
        return messages.map((message, index) => (
            <MessageContent message={message} agent={agentName} key={index} />
        ));
    };
    const stopGeneration = () => {
        if (controllerRef.current) {
            controllerRef.current.abort();
        }
        setIsTyping(false);
    };

    return (
        <div className="container-fluid d-flex flex-column vh-100 p-0 mx-auto" style={{ maxWidth: '800px' }}>
            <div className="d-flex shadow-sm justify-content-start align-items-center p-3">
                <img src={Logo} alt='GoBot Logo' className='gobot-logo' />
                {/* Chat header */}
                <div className="bg-white p-3 top-title" style={{ flex: 1 }}>
                    <h1 className="h5 font-weight-bold">GoBot {agent.replace('_', ' ')}</h1>
                </div>
            </div>

            <div className="chat-body" ref={chatBodyRef}>
                {/* Welcome message */}
                {messages.length === 0 && (
                    // <div className="welcome-message">
                    //     <h6>Welcome to {companyName} Support</h6>
                    //     <p>How can we help you today?</p>
                    // </div>
                    <MessageContent
                        message={{
                            id: Date.now().toString(),
                            type: 'ai',
                            timestamp: Date.now().toString(),
                            content: `Hi I am GoBot. How can we help today?`,
                        }}
                        agent={agentName}
                        key={Date.now().toString()}
                    />
                )}

                {/* Messages */}
                <div className="messages-container">
                    {renderMessages()}
                    {/* Typing indicator */}
                    {isTyping ? isRendering ?
                        <div
                            className={`message agent-message`}
                        >
                            <div className="message-container">
                                <div className="message-title">{agentName}</div>
                                <div className="message-content m-0"> <ReactMarkdown>{currentMessage}</ReactMarkdown></div>
                            </div>
                            <div className="typing-indicator">
                                <div className="dot"></div>
                                <div className="dot"></div>
                                <div className="dot"></div>
                            </div>
                        </div>
                        : (
                            <div className="typing-indicator">
                                <div className="dot"></div>
                                <div className="dot"></div>
                                <div className="dot"></div>
                            </div>
                        ) : ''}
                    {nextStep && nextStep !== '' && (
                        <div className="text-center">
                            <p className="font-weight-bold">{nextStep}</p>
                        </div>
                    )}
                    <div ref={messagesEndRef} />
                </div>

            </div>
            {/* Error message */}
            <div className="p-3 text-center text-danger">{error}</div>

            {/* Chat footer with input */}
            <div className="chat-footer">
                {/* Suggested tools */}
                {/* messages.length === 0 &&  */}
                {suggestedTools.length ?
                    <div className="suggested-tools-container overflow-auto pb-1">
                        <div className="row1 d-flex flex-nowrap pb-1">
                            {currentSuggestedTools.map((tool, index: number) => (
                                index % 2 == 0 ?
                                    <div
                                        key={index}
                                        className="card suggested-tool-card mx-2"
                                        onClick={() => handleSendToolMessage(tool.action)}
                                        style={{ minWidth: '180px', width: 'fit-content', cursor: 'pointer' }}
                                    >
                                        <div className="card-body d-flex align-items-start">
                                            <span className="ms-2 me-1">{tool.icon}</span>
                                            <span>{tool.text}</span>
                                        </div>
                                    </div> : ''
                            ))}
                        </div>
                        <div className="row2 d-flex flex-nowrap pb-1">
                            {currentSuggestedTools.map((tool, index: number) => (
                                index % 2 != 0 ?
                                    <div
                                        key={index}
                                        className="card suggested-tool-card mx-2"
                                        onClick={() => handleSendToolMessage(tool.action)}
                                        style={{ minWidth: '180px', width: 'fit-content', cursor: 'pointer' }}
                                    >
                                        <div className="card-body d-flex align-items-start">
                                            <span className="ms-2 me-1">{tool.icon}</span>
                                            <span>{tool.text}</span>
                                        </div>
                                    </div> : ''
                            ))}
                        </div>
                    </div> : ''}
                {/* Attachment previews */}
                {attachments.length > 0 && (
                    <div className="attachment-previews">
                        {renderAttachmentPreviews()}
                    </div>
                )}

                <div className="message-input-container mt-2">
                    <button
                        className="new-chat"
                        onClick={newSession}
                    >
                        <PlusCircle size={18} />
                    </button>
                    <input
                        type="content"
                        className="message-input"
                        placeholder="Type your message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        // onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                        onKeyUp={(e) => e.key === 'Enter' && handleSendMessage()}
                    />

                    <div className="message-actions">
                        {/* <button
                                className="attachment-button"
                                onClick={() => fileInputRef?.current?.click()}
                            >
                                <Paperclip size={18} />
                            </button> */}

                        {isTyping ?
                            <button
                                className="stop-button"
                                onClick={stopGeneration}
                                disabled={!isTyping}
                            >
                                <StopCircle size={18} />
                            </button>
                            :
                            <button
                                className="send-button"
                                onClick={handleSendMessage}
                                disabled={newMessage.trim() === '' && attachments.length === 0}
                            >
                                <Send size={18} />
                            </button>
                        }
                    </div>

                    <input
                        type="file"
                        ref={fileInputRef}
                        style={{ display: 'none' }}
                        onChange={handleFileSelect}
                        multiple
                    />
                </div>
                <p className='powered-by text-center text-muted mt-1 mb-0'>Powered by <a href='https://www.maxmobility.in/'>MaxMobility</a></p>
            </div>
        </div>
    );
};