
import { JSX } from "react";

export type Attachment = {
  name: string;
  type: string;
  size: number;
  data: string | ArrayBuffer | null;
}

export type Token = {
  token: string;
  exp: number;
}

export type Chart = {
  json?: string,
  summary?: string,
  url?: string
};

export interface Message {
  message_id: string,
  content: string;
  // role: 'user' | 'assistant' | 'system';
  type: 'human' | 'ai' | 'system';
  timestamp: Date | string;
  attachments?: Attachment[];
  chart?: Chart;
  isStreaming?: boolean;
}

export type SocketMessage = {
  id: string,
  content: string;
  type: 'human' | 'ai' | 'system';
  chart?: Chart;
  timestamp: Date | string;
}

export type SuggestedTools = {
  icon: JSX.Element | string;
  text: string;
  action: string;
}

export interface ChatSession {
  id: string;
  sessionId: string;
  title: string;
  messages: Message[];
  createdAt: Date | string;
  updatedAt: Date | string;
  userId?: string;
  user?: string;
  summary?: string;
  type: string;
  project?: string;
  lastUpdatedAt?: string;
  refreshToken?: Token;
  authToken?: Token;
}

export interface BotState {
  status: 'idle' | 'thinking' | 'writing' | 'loading_image' | 'chart_generation' | 'workflow_execution';
  thinkingSteps?: string[];
  currentMessage?: string;
}

export interface ChatState {
  botState: BotState;
  isLoading: boolean;
  error: string | null;
  messages: Message[];
  suggestedTools: SuggestedTools[];
}

export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  backgroundColor: string;
}

export interface WidgetConfig {
  projectId: string;
  userId: string;
  apiKey: string;
  botName?: string;
  agentType?: string;
  theme: ThemeConfig;
}

export interface AuthState {
  sessions: ChatSession[];
  currentSession: ChatSession | null;
  sessionId: string | null;
  authToken: string | null;
  refreshToken: string | null;
}

export interface Response {
  status: boolean,
  message: string,
  data: null | unknown | Record<string, unknown>
  errors: null | unknown
}

export interface Session {
  session_id: string;
  user_id?: string
  user?: string;
  summary?: string;
  title?: string;
  agent_type: string;
  project?: string;
  created_at: string;
  last_updated?: string;
  refreshToken?: string;
  authToken?: string;
}
