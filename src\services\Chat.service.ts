import Base from './Base.service';
import { CHAT, CHAT_MESSAGES, CHAT_SESSIONS, CHAT_SESSION, SESSION_TOKEN } from '../config/endpoints.config';
import { store } from '../store/store';
import { addMessage, setMessages } from '../store/slices/chatSlice';
import { setSessions, setCurrentSession, setRefreshToken, setToken, setSessionId, deleteSession } from '../store/slices/authSlice';
import { setDetails } from '../store/slices/commonSlice';
import { v4 as uuid } from 'uuid';
import { Chart, Message, Session, Token, ChatSession } from '../types/chat';
import config from '@/config';

interface ChatResponse {
    session_id?: string;
    access_token?: Token;
    refresh_token?: Token;
    user_id?: string;
    created_at: string;
    last_updated: string;
    project?: string;
    user?: string;
    agent_type: string;
    summary?: string;
    title?: string;
}

interface StreamChunk {
    chunk: string;
    finished: boolean;
    type?: string;
    final_message?: string;
    message_id?: string;
    chart?: Chart
}

class ChatService extends Base {
    async initSession(token: string, userId: string = '', project: string = '', botName: string = '', type = '', ipAddress: string = ''): Promise<ChatSession | Record<string, unknown>> {
        try {
            const url = new URL(`${this.baseURL}${CHAT.replace('/{session}', '')}`);
            let message = '';
            if (project != '') {
                message = `Hi I am ${botName}. How can I help with ${project} today?`
            } else {
                message = `Hi I am ${botName}. How can I help today?`
            }
            if (ipAddress == '') {
                ipAddress = await this.fetchIp();
            }
            const response = await this.post(url.toString(), {
                user_id: userId || '',
                message: message,
                project: project,
                agent_type: type,
                ip_address: ipAddress
            }, false, {
                'Authorization': `Bearer ${token}`
            });
            console.log(response?.data);
            if (response && response.data && response.data?.status) {
                const data: ChatResponse = response.data.data;
                const session: ChatSession = {
                    sessionId: data?.session_id || '',
                    authToken: data?.access_token,
                    refreshToken: data?.refresh_token,
                    userId: data?.user_id,
                    project: data?.project,
                    type: type,
                    user: data?.user,
                    summary: data?.summary || '',
                    title: data?.title || '',
                    createdAt: data?.created_at || '',
                    id: data?.session_id || '',
                    messages: [],
                    updatedAt: data?.last_updated || ''
                }
                store.dispatch(setDetails({
                    token: token,
                    userId: data?.user_id,
                    project: data?.project,
                    botName: botName,
                    type: type,
                    ipAddress: ipAddress
                }));
                store.dispatch(setCurrentSession(session));
                store.dispatch(setMessages([]))
                return session;
            }
            return {};
        } catch (error) {
            console.error('Error initializing session:', error);
            throw error;
        }
    }

    async getSessionToken(session: ChatSession) {
        try {
            const url = new URL(`${this.baseURL}${SESSION_TOKEN.replace('{session}', session.id)}`);
            const response = await this.post(url.toString(), {}, true);
            if (response && response.status === 200) {
                const res: {
                    access_token: Token;
                    refresh_token: Token;
                } = response.data.data;

                // store.dispatch(setToken({
                //     token: res.access_token.token,
                //     exp: res.access_token.exp
                // }));
                // store.dispatch(setRefreshToken({
                //     token: res.refresh_token.token,
                //     exp: res.refresh_token.exp
                // }));
                // store.dispatch(setSessionId(sessionId));
                store.dispatch(setCurrentSession({ ...session, authToken: res.access_token, refreshToken: res.refresh_token }));
                return res;
            }
            return {};
        } catch (error) {
            console.error('Error getting session token:', error);
            throw error;
        }
    }

    // async sendMessage(sessionId: string, message: string, project: string, userId: string, signal?: AbortSignal): Promise<EventSource> {
    //     if (!sessionId) throw new Error('Session ID is required');

    //     const url = new URL(`${this.baseURL}${CHAT.replace('{session}', sessionId)}`);
    //     url.searchParams.append('message', message);
    //     url.searchParams.append('user_id', userId);
    //     url.searchParams.append('project', project);

    //     const source = new EventSource(url.toString(), {
    //         headers: {
    //             'Authorization': `Bearer ${this.getToken()}`
    //         }
    //     });

    //     if (signal) {
    //         signal.addEventListener('abort', () => source.close());
    //     }

    //     return source;
    // }
    async getMessages(sessionId: string, limit: number = 10, page: number = 1): Promise<Message[]> {
        try {
            if (!sessionId) throw new Error('Session ID is required');
            const url = new URL(`${this.baseURL}${CHAT_MESSAGES.replace('{session}', sessionId)}?limit=${limit}&page=${page}`);
            const response = await this.get(url.toString(), {}, true)
            if (response && response.status === 200) {
                const messages: Message[] = response.data.data
                return messages;
            }
            return [];

        } catch (error: unknown) {
            console.error('Error getting messages:', error);
            return [];
        }
    }

    async getSessions(userId: string = '', ipAddress: string = ''): Promise<ChatSession[]> {
        try {
            const url = new URL(`${this.baseURL}${CHAT_SESSIONS}`);
            if (ipAddress == '') {
                ipAddress = await this.fetchIp();
            }
            const response = await this.post(url.toString(), {
                user_id: userId,
                ip_address: ipAddress
            }, true)
            if (response && response.status === 200) {
                const sessions: ChatSession[] = response.data.data?.map((item: Session) => {
                    return {
                        user: item?.user,
                        summary: item?.summary,
                        title: item?.title,
                        type: item?.agent_type,
                        project: item?.project,
                        createdAt: item?.created_at,
                        updatedAt: item?.last_updated,
                        lastUpdatedAt: item?.last_updated,
                        sessionId: item?.session_id,
                        id: item?.session_id
                    }
                });
                store.dispatch(setSessions(sessions));
                return sessions;
            }
            return [];
        } catch (error: unknown) {
            console.error('Error getting sessions:', error);
            return [];
        }
    }

    async deleteSession(sessionId: string) {
        try {
            const url = new URL(`${this.baseURL}${CHAT_SESSION.replace('{session}', sessionId)}`);
            const response = await this.delete(url.toString(), true);
            if (response && response.status === 200) {
                store.dispatch(deleteSession(sessionId));
                return true;
            }
            return false;
        } catch (error: unknown) {
            console.error('Error deleting session:', error);
            return false;
        }
    }
    // async sendMessage(sessionId: string, data: { message: string, project: string, user_id: string }, signal: AbortSignal | undefined, setCurrentMessage: (message: string) => void, setError: (error: unknown) => void): Promise<void> {
    //     const makeRequest = async (token: string) => {
    //         if (!sessionId) throw new Error('Session ID is required');
    //         const url = new URL(`${this.baseURL}${CHAT.replace('{session}', sessionId)}`);
    //         const response = await axios.post(url.toString(),
    //             { ...data },
    //             {
    //                 headers: {
    //                     'Content-Type': 'application/json',
    //                     'Authorization': `Bearer ${token}`
    //                 },
    //                 responseType: "blob",
    //                 signal: signal
    //             }
    //         );

    //         const reader = response.data.stream().getReader();
    //         const decoder = new TextDecoder();

    //         let receivedText = "";

    //         while (true) {
    //             const { done, value } = await reader.read();
    //             if (done) break;
    //             const chunkText = decoder.decode(value, { stream: true }).replace('data: ', '')
    //             console.log(chunkText)
    //             const chunkData: StreamChunk = JSON.parse(chunkText);
    //             receivedText += chunkData.chunk;
    //             setCurrentMessage(chunkData.chunk);
    //             // if (chunkData.finished) {

    //             // }
    //         }
    //         store.dispatch(addMessage({
    //             id: Date.now().toString(),
    //             content: receivedText,
    //             type: 'agent',
    //             timestamp: Date.now().toString(),
    //         }));
    //         setCurrentMessage('END');
    //     };

    //     try {
    //         await makeRequest(this.getToken());
    //     } catch (error: unknown) {
    //         if (error instanceof AxiosError && error?.response?.status === 401) {
    //             // Try to refresh the token
    //             const refreshResult = await this.refreshToken();
    //             if (refreshResult.status && refreshResult.data?.access_token) {
    //                 // Retry the request with the new token
    //                 try {
    //                     await makeRequest(refreshResult.data.access_token);
    //                     return;
    //                 } catch (retryError) {
    //                     setError(retryError);
    //                 }
    //             }
    //         }
    //         setError(error);
    //     }
    // }


    async sendMessage(
        sessionId: string,
        data: { message: string; project: string; user_id: string },
        agentType: string,
        signal: AbortSignal | undefined,
        setCurrentMessage: (message: string) => void,
        setError: (error: unknown) => void
    ): Promise<void> {
        const makeRequest = async (token: string) => {
            if (!sessionId) throw new Error("Session ID is required");
            const url = new URL(`${this.baseURL}${CHAT.replace("{session}", sessionId)}`);
            const headers = await this.getHeaders({ token: true })
            console.log(headers)
            fetch(url.toString(), {
                method: "POST",
                headers: headers || {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({ ...data, agent_type: agentType }),
                signal: signal,
            }).then(async (response) => {
                try {
                    const reader = response.body?.getReader();
                    if (!reader) {
                        throw new Error("Readable stream not supported");
                    }
                    console.log("reading stream")
                    const decoder = new TextDecoder();
                    let receivedText = "",
                        finalMessage = "",
                        messageId = "",
                        chart: Chart | undefined;

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;
                        let chunkText = decoder.decode(value, { stream: true }).trim();
                        try {
                            chunkText = chunkText.replace("### Response", "")

                            chunkText.split('res: ').forEach((chunk) => {
                                chunk = chunk.replace('res: ', '').replace("}\n\n", "}").replace(/^res:\s*/, "")
                                if (chunk && chunk != '' && chunk !== "### Response") {
                                    const chunkData: StreamChunk = JSON.parse(chunk);
                                    if (chunkData?.type == 'indicator') {
                                        console.log(chunkData)
                                    }
                                    else {
                                        receivedText += chunkData.chunk;
                                        setCurrentMessage(chunkData.chunk);
                                    }
                                    if (chunkData?.finished && chunkData?.final_message) {
                                        finalMessage = chunkData?.final_message || ''
                                        messageId = chunkData?.message_id || ''
                                        chart = chunkData?.chart
                                    }
                                }
                            })

                        } catch (e) {
                            console.error("Error parsing JSON chunk:", chunkText, e);
                            setError(e);
                        }
                    }

                    store.dispatch(
                        addMessage({
                            message_id: messageId && messageId != '' ? messageId : uuid(),
                            content: finalMessage != '' ? finalMessage : receivedText,
                            type: "ai",
                            chart: chart,
                            timestamp: new Date().toISOString(),
                        })
                    );
                    setCurrentMessage("END");
                } catch (e) {
                    console.log(e)
                    throw e;
                }
            }).catch((e) => {
                console.log(e)
                throw e;
            })
        };

        try {
            await makeRequest(this.getToken());
        } catch (error: unknown) {
            console.log(error)
            if (error instanceof Error && error.message.includes("401")) {
                // Try to refresh the token
                const refreshResult = await this.refreshToken();
                if (refreshResult.status && refreshResult.data?.access_token) {
                    // Retry with new token
                    try {
                        await makeRequest(refreshResult?.data?.access_token?.token);
                        return;
                    } catch (retryError) {
                        setError(retryError);
                    }
                }
            }
            setError(error);
        }
    }

    async fetchIp() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data?.ip || this.getDeviceId();
        } catch (error) {
            console.error('Error fetching IP address:', error);
            return this.getDeviceId();
        }
    }

    getDeviceId() {
        let deviceId = localStorage.getItem(config.DEVICE_ID_KEY);
        if (!deviceId) {
            deviceId = uuid();
            localStorage.setItem(config.DEVICE_ID_KEY, deviceId);
        }
        return deviceId;
    }

}

export const chatService = new ChatService();
