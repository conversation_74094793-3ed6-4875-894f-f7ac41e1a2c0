
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Message, Session, ChatState, SuggestedTools, ChatSession, BotState } from '../../types/chat';

const initialState: ChatState = {
  messages: [],
  botState: {
    status: 'idle',
    thinkingSteps: []
  },
  isLoading: false,
  error: null,
  suggestedTools: []
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action: PayloadAction<Message>) => {
      state.messages.push(action.payload);
    },
    extendMessages: (state, action: PayloadAction<Message[]>) => {
      state.messages = [...action.payload, ...state.messages];
    },
    setMessages: (state, action: PayloadAction<Message[]>) => {
      state.messages = action.payload;
    },
    updateMessage: (state, action: PayloadAction<Message>) => {
      const index = state.messages.findIndex(m => m.message_id === action.payload.message_id);
      if (index !== -1) {
        state.messages[index] = action.payload;
      }
    },
    setBotState: (state, action: PayloadAction<BotState>) => {
      state.botState = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setSuggestedTools: (state, action: PayloadAction<SuggestedTools[]>) => {
      state.suggestedTools = action.payload;
    }
  }
});

export const {
  addMessage,
  updateMessage,
  setMessages,
  extendMessages,
  setSuggestedTools,
  setBotState,
  setLoading,
  setError
} = chatSlice.actions;

export default chatSlice.reducer;