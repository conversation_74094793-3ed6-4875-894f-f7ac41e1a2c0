
const production = import.meta.env.VITE_ENVIRONMENT == 'production';
const config = {
  env: import.meta.env.VITE_ENVIRONMENT,
  appName: '' + import.meta.env.VITE_APP_NAME || 'Max Agent',
  botName: '' + import.meta.env.VITE_BOT_NAME || 'GoBot',
  apiTimeout: production ? 10 * 1000 : 20 * 1000, // 10 or 20 sec
  baseUrl: '' + import.meta.env.VITE_API_URL,
  refreshTimeout: production ? 60 : 10,
  xApiToken: '' + import.meta.env.VITE_API_TOKEN,
  seriesName: '' + import.meta.env.VITE_APP_NAME || 'Max Agent',
  DEVICE_ID_KEY: 'gobot_device_id',
};

Object.freeze(config); // Makes the config object immutable

export default config;
