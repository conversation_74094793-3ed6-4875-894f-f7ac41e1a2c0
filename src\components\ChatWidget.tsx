import React, { useState, useEffect } from 'react';
import { Provider } from 'react-redux';
import { store } from '../store/store';
import { WidgetConfig } from '../types/chat';
import { MessageCircle, X, Maximize2, Minimize2, Plus, Mic, ChevronLeft, AlignRight, Send } from 'lucide-react';
import ChatInput from './ChatInput';
import ChatArea from './ChatArea';
import { useChat } from '@/hooks/useChat';

interface ChatWidgetProps {
  config: WidgetConfig;
}

const ChatWidget: React.FC<ChatWidgetProps> = ({ config }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMinimized, setIsMinimized] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { currentSession, isLoading } = useChat();
  const hasMessages = currentSession && currentSession.messages.length > 0;
  const suggestions = [
    { icon: "🖼️", text: "Get Company Details", color: "text-green-600" },
    { icon: "💬", text: "Get Product & Service Support", color: "text-orange-600" },
    { icon: "📅", text: "Schedule Sales Demo", color: "text-blue-600" },
    { icon: "📄", text: "Get Sales Report", color: "text-blue-600" }
  ];

  const chatHistory = [
    { id: 1, title: "GoBot Sales", summary: "Product inquiry discussion", timestamp: "2 hours ago" },
    { id: 2, title: "Technical Support", summary: "API integration help", timestamp: "1 day ago" },
    { id: 3, title: "Feature Request", summary: "New dashboard features", timestamp: "3 days ago" },
    { id: 4, title: "General Questions", summary: "Platform overview", timestamp: "1 week ago" },
  ];

  useEffect(() => {
    document.documentElement.className = config.theme.mode;
  }, [config.theme]);

  if (isMinimized) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setIsMinimized(false)}
          className="w-16 h-16 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center transition-all hover:scale-110 relative group"
        >
          <MessageCircle className="w-8 h-8" />
          <div className="absolute -top-2 -right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>

          {/* Tooltip */}
          <div className="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Chat with us!
          </div>
        </button>
      </div>
    );
  }

  return (
    <Provider store={store}>
      <div className={`fixed z-50 transition-all duration-300 ${isExpanded
        ? 'inset-4'
        : 'bottom-6 right-6 w-96 h-[600px]'
        } bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden`}>

        {/* Widget Header */}
        <div className='border-b border-gray-200 dark:border-gray-700 bg-blue-600 text-white rounded-t-lg'>
          <div className="flex items-center justify-between p-4 pb-1">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <MessageCircle className="w-6 h-6" />
              </div>
              <div>
                <h3 className="font-semibold">Chat</h3>
                <p className="text-xs text-blue-100">Questions? Chat with us!</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">

              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 rounded-lg hover:bg-white/10 transition-colors"
              >
                <AlignRight className="w-5 h-5" />
              </button>
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-2 rounded-lg hover:bg-white/10 transition-colors"
                title={isExpanded ? "Minimize" : "Expand"}
              >
                {isExpanded ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </button>

              <button
                onClick={() => setIsMinimized(true)}
                className="p-2 rounded-lg hover:bg-white/10 transition-colors"
                title="Close"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
          {/* Status Indicator */}
          <div className="px-4 pb-2">
            <div className="flex items-center space-x-2 ml-3 text-sm text-white-600 dark:text-white-400">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>We're online</span>
            </div>
          </div>
        </div>

        {/* Main Chat Area */}
        <div className={"flex flex-row flex-1"}>
          {/* Sidebar */}
          <div className={`${sidebarOpen ? isExpanded ? 'w-60' : 'w-70 flex-1' : 'w-0 hidden'} transition-all duration-300 bg-gray-50 border-r border-gray-200 flex flex-col overflow-hidden`}>
            {/* Sidebar Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">Chats</h3>
                <button
                  onClick={() => setSidebarOpen(false)}
                  className="p-1 rounded hover:bg-gray-200 transition-colors"
                >
                  <ChevronLeft className="w-4 h-4 text-gray-500" />
                </button>
              </div>

              <button className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors text-sm">
                <Plus className="w-4 h-4" />
                <span>New Chat</span>
              </button>
            </div>

            {/* Chat History */}
            <div className="flex-1 overflow-y-auto p-2">
              {chatHistory.map((chat) => (
                <div
                  key={chat.id}
                  className="p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors mb-2"
                >
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{chat.title}</p>
                    <p className="text-xs text-gray-500 truncate">{chat.summary}</p>
                    <p className="text-xs text-gray-400 mt-1">{chat.timestamp}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {isExpanded || !sidebarOpen ?
            <div className={`flex flex-1 flex-col justify-end h-full`}>
              {/* Chat area */}
              {(!currentSession || currentSession.messages.length === 0) ?
                /* Welcome Screen */
                <div className="flex-1 flex flex-col items-center justify-center p-6 bg-gray-50">
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                      How can I help with?
                    </h2>
                  </div>

                  {/* Suggestion Buttons */}
                  <div className="grid grid-cols-2 gap-3 mb-8 w-full max-w-md">
                    {suggestions.slice(0, 4).map((suggestion, index) => (
                      <button
                        key={index}
                        className="flex items-center space-x-3 p-4 bg-white rounded-2xl border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all text-left"
                      >
                        <span className="text-1xl">{suggestion.icon}</span>
                        <span className="font-medium text-gray-900" style={{ fontSize: '13px' }}>
                          {suggestion.text}
                        </span>
                      </button>
                    ))}
                  </div>

                  {/* More Button */}
                  {/* <button className="flex items-center space-x-3 p-3 bg-white rounded-2xl border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all">
                    <span className="text-gray-600 text-sm font-medium">More</span>
                  </button> */}
                </div>
                : <ChatArea />
              }
              {/* Input */}
              <div className={hasMessages ? '' : 'flex justify-center pb-8'}>
                <div className={hasMessages ? 'w-full' : 'w-full max-w-4xl px-4'}>
                  <ChatInput />
                </div>
              </div>
            </div> : ""
          }

        </div>

        {/* Footer */}
        <div className="p-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            Powered by{' '}
            <a href="https://maxmobility.in" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
              MaxMobility
            </a>
          </p>
        </div>
      </div>
    </Provider >
  );
};

export default ChatWidget;
